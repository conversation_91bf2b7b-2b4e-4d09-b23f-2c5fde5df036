# Dependency Update Report

## Overview
This report documents the major dependency updates performed on the ESW API project, comparing the current branch with the development branch.

## Major Framework Updates

### NestJS Framework (v9 → v11)
**Packages Updated:**
- `@nestjs/common`: `^9.0.0` → `^11.1.3`
- `@nestjs/core`: `^9.0.0` → `^11.1.3`
- `@nestjs/platform-express`: `^9.0.0` → `^11.1.3`
- `@nestjs/apollo`: `^12.0.9` → `^13.1.0`
- `@nestjs/graphql`: `^12.0.9` → `^13.1.0`
- `@nestjs/cache-manager`: `3.0.0` → `^3.0.1`

**Development Dependencies:**
- `@nestjs/cli`: `^9.0.0` → `^11.0.7`
- `@nestjs/schematics`: `^9.0.0` → `^11.0.5`
- `@nestjs/testing`: `^9.0.0` → `^11.1.3`

**Rationale:** Major version upgrade to leverage latest NestJS features, performance improvements, and security patches. NestJS v11 includes better TypeScript support, improved GraphQL integration, and enhanced performance.

### Sentry Monitoring (v8 → v9)
**Packages Updated:**
- `@sentry/nestjs`: `^8.42.0` → `^9.30.0`
- `@sentry/profiling-node`: `^8.42.0` → `^9.30.0`
- `@sentry/cli`: `^2.39.1` → `^2.46.0`

**Removed:**
- `@ntegral/nestjs-sentry`: `^4.0.1` (replaced by official Sentry NestJS integration)

**Rationale:** Sentry v9 provides better error tracking, performance monitoring, and official NestJS integration. The removal of `@ntegral/nestjs-sentry` is due to Sentry now providing official NestJS support.

### Apollo GraphQL Server
**Packages Updated:**
- `@apollo/server`: `^4.9.5` → `^4.12.2`

**Rationale:** Bug fixes, security updates, and improved GraphQL performance.

### Database & ORM
**Packages Updated:**
- `@prisma/client`: `^5.5.2` → `^5.22.0`
- `prisma`: `^5.5.2` → `^5.22.0`

**Rationale:** Latest Prisma version with performance improvements and bug fixes.

### TypeScript & Build Tools
**Packages Updated:**
- `typescript`: `^4.7.4` → `^5.8.3`
- `reflect-metadata`: `^0.1.13` → `^0.2.2`
- `ts-loader`: `^9.2.3` → `^9.5.2`

**Rationale:** TypeScript 5.x provides better type checking, performance improvements, and new language features.

### Utility Libraries
**Packages Updated:**
- `class-validator`: `^0.14.0` → `^0.14.2`
- `compression`: `^1.7.4` → `^1.8.0`
- `dataloader`: `^2.2.2` → `^2.2.3`
- `dotenv`: `^16.3.1` → `^16.5.0`
- `graphql`: `^16.8.1` → `^16.11.0`
- `mime`: `^4.0.1` → `^4.0.7`
- `moment`: `^2.29.4` → `^2.30.1`
- `pg`: `^8.11.3` → `^8.16.0`
- `rxjs`: `^7.2.0` → `^7.8.2`

**Type Definitions Updated:**
- `@types/compression`: `^1.7.5` → `^1.8.1`
- `@types/express-session`: `^1.18.1` → `^1.18.2`
- `@types/lodash`: `^4.14.202` → `^4.17.17`
- `@types/pg`: `^8.10.7` → `^8.15.4`

## Packages Kept Stable (Redis Ecosystem)
The following Redis-related packages were intentionally kept at their current versions due to compatibility requirements:
- `redis`: `4.6.12` (maintained for redis-om compatibility)
- `redis-om`: `0.4.7` (requires Redis v4)
- `redis-lock`: `1.0.0`
- `connect-redis`: `^8.0.2`
- `@keyv/redis`: `4.2.0`

**Rationale:** Redis-om library does not support Redis v5, requiring the project to maintain Redis v4 compatibility.

## Issues Fixed

### 1. Sentry profilesSampleRate Deprecation ✅ FIXED
**Location:** `src/infra/framework/instrument.ts:19`
**Issue:** `profilesSampleRate` is deprecated in Sentry v9
**Solution:** Replaced with `profileSessionSampleRate: 0.005` and `profileLifecycle: 'trace'` as per Sentry v9 documentation
**Status:** ✅ Completed

### 2. NestJS Route Pattern Warning ✅ FIXED
**Location:** `src/infra/session/session.module.ts:41`
**Issue:** LegacyRouteConverter warning for route pattern using `'*'` in `.forRoutes('*')`
**Solution:** Updated to use `{ path: '*', method: RequestMethod.ALL }` syntax compatible with path-to-regexp v8
**Status:** ✅ Completed

### 3. Unused Legacy Sentry Package ✅ REMOVED
**Issue:** `@ntegral/nestjs-sentry` package was still listed in dependencies despite being replaced
**Solution:** Removed using `npm uninstall @ntegral/nestjs-sentry`
**Status:** ✅ Completed

## Next Steps
1. ✅ Fix deprecated Sentry `profilesSampleRate` configuration
2. ✅ Investigate and resolve route pattern warnings
3. ✅ Remove unused legacy Sentry package
4. Test all API functionality to ensure compatibility
5. Monitor for any additional deprecation warnings during development
